'use client'

import { useEffect, useState } from 'react'

export function SimpleAnalyticsProvider({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    const apiKey = process.env.NEXT_PUBLIC_POSTHOG_API_KEY
    const apiHost = process.env.NEXT_PUBLIC_POSTHOG_API_HOST || 'https://us.i.posthog.com'

    if (!apiKey) {
      console.warn('PostHog API key not found')
      return
    }

    // Add error boundary for dynamic import
    const loadPostHog = async () => {
      try {
        // Dynamic import with error handling
        const posthogModule = await import('posthog-js')
        const posthog = posthogModule.default

        // Prevent multiple initializations
        if (posthog.__loaded) {
          console.log('PostHog already loaded')
          setIsInitialized(true)
          return
        }

        console.log('🚀 Initializing PostHog (simple setup)...')

        posthog.init(api<PERSON>ey, {
          api_host: apiHost,

          // Essential settings only
          debug: process.env.NODE_ENV === 'development',
          capture_pageview: true,
          capture_pageleave: true,
          autocapture: false,

          // Simple error handling
          on_request_error: (error) => {
            console.warn('PostHog request error (non-blocking):', error)
            return false // Don't throw
          },

          // Success callback
          loaded: (posthogInstance) => {
            console.log('✅ PostHog loaded successfully')
            setIsInitialized(true)

            // Send a test event
            posthogInstance.capture('posthog_simple_init', {
              timestamp: new Date().toISOString(),
              environment: process.env.NODE_ENV
            })
          }
        })

      } catch (error) {
        console.error('PostHog initialization failed:', error)
        // Set initialized to true to prevent infinite retries
        setIsInitialized(true)
      }
    }

    loadPostHog()
  }, [])

  // Simple status display in development
  if (process.env.NODE_ENV === 'development') {
    return (
      <>
        {children}
        <div className="fixed top-4 right-4 bg-white p-2 rounded shadow text-xs z-50">
          <div>PostHog: {isInitialized ? '✅ Ready' : '⏳ Loading'}</div>
          <div>API: {process.env.NEXT_PUBLIC_POSTHOG_API_KEY?.substring(0, 8)}...</div>
          {isInitialized && (
            <button
              type="button"
              onClick={async () => {
                try {
                  // Dynamic import for test event with better error handling
                  const posthogModule = await import('posthog-js')
                  const posthog = posthogModule.default

                  if (!posthog || !posthog.__loaded) {
                    console.warn('PostHog not ready for test event')
                    return
                  }

                  console.log('🧪 Sending test event...')
                  posthog.capture('manual_test_event', {
                    timestamp: new Date().toISOString(),
                    test: true
                  })
                  console.log('✅ Test event sent')
                } catch (error) {
                  console.error('Failed to send test event:', error)
                }
              }}
              className="mt-1 bg-blue-500 text-white px-2 py-1 rounded text-xs"
            >
              Test Event
            </button>
          )}
        </div>
      </>
    )
  }

  return <>{children}</>
}
